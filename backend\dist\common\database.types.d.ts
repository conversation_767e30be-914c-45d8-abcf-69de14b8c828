export type Json = unknown;
export type enum_ideas_estado = 'descartada' | 'implementada' | 'pendiente';
export type enum_ideas_prioridad = 'Alta' | 'Baja' | 'Media' | 'Urgente';
export type estado_contrato = 'activo' | 'borrador' | 'cancelado' | 'en_pausa' | 'finalizado' | 'firmado' | 'pendiente_firma' | 'renovado';
export type estado_evaluacion_enum = 'descartado' | 'pendiente' | 'revisado';
export type estado_factura = 'anulada' | 'borrador' | 'cancelada' | 'emitida' | 'enviada' | 'pagada' | 'vencida';
export type estado_hallazgo_enum = 'identificado' | 'pendiente_revision_humana';
export type estado_mejora = 'aplicado' | 'aprobado_para_aplicar' | 'descartado' | 'pendiente_revision_humana';
export type estado_mejora_agente_enum = 'aplicado' | 'aprobado' | 'descartado' | 'pendiente';
export type estado_proceso_cliente_enum = 'definicion_completa' | 'definicion_parcial' | 'identificado' | 'pendiente_revision_humana';
export type estado_tarea = 'Bloqueada' | 'Completada' | 'En Progreso' | 'En Revisión' | 'Pendiente';
export type evaluaciones_estado_enum = 'mejoras_aplicadas' | 'pendiente_mejoras' | 'pendiente_revision' | 'revisado';
export type grabacion_entidad_tipo = 'pregunta_cliente' | 'proceso_cliente' | 'tarea_cliente';
export type grabacion_estado_procesamiento = 'completado' | 'en_procesamiento' | 'error' | 'pendiente';
export type grabacion_tipo = 'solo_audio' | 'video_pantalla';
export type intervention_status = 'cancelado' | 'fallido' | 'pendiente' | 'resuelto';
export type pregunta_estado = 'en_procesamiento_ia' | 'pendiente_respuesta' | 'procesada' | 'respondida';
export type pregunta_visibilidad = 'interno_aceleralia' | 'portal_cliente';
export type prioridad_general_enum = 'Alta' | 'Baja' | 'Media' | 'Urgente';
export type prioridad_tarea = 'Alta' | 'Baja' | 'Media' | 'Urgente';
export type reto_estado = 'completado' | 'expirado' | 'pendiente';
export type reto_tipo_accion = 'definir_duracion_proceso' | 'definir_proceso' | 'definir_tarea' | 'responder_pregunta';
export type rol_decision_lead_enum = 'Influenciador' | 'Otro' | 'Tomador de decision';
export type sop_guia_type_enum = 'Guia' | 'SOP';
export type task_status = 'cancelled' | 'completed' | 'failed' | 'interrupted' | 'pending' | 'ready' | 'running';
export type thread_request_type_enum = 'API' | 'chat';
export type tipo_complejidad_automatizacion = 'alta' | 'baja' | 'media' | 'muy_alta' | 'pendiente_de_analisis';
export type tipo_frecuencia_periodo = 'anual' | 'diario' | 'indefinido' | 'mensual' | 'semanal' | 'trimestral';
export type tipo_hallazgo_enum = 'deficit_gobernanza_datos' | 'equipamiento_inadecuado' | 'falta_estandarizacion' | 'ineficiencia' | 'ladron_tiempo' | 'oportunidad_mejora' | 'riesgo_identificado';
export type tipo_prioridad_automatizacion = 'alta' | 'baja' | 'critica' | 'media' | 'pendiente_de_analisis';
export type tipo_proceso_enum = 'Externo' | 'Interno';
export type tipo_relacion_empresa_enum = 'Cliente' | 'Colaborador' | 'Lead' | 'Otro';
export type urgencia_tarea_enum = 'No Urgente' | 'Urgente';
export interface Agentes {
    id: string;
    nombre: string;
    descripcion: string | null;
    system_prompt: string;
    activo: boolean | null;
    created_at: Date | null;
    updated_at: Date | null;
    selected_llm_model_id: string | null;
    db_schema: boolean | null;
    temperature: number | null;
}
export interface AgentesInput {
    id?: string;
    nombre: string;
    descripcion?: string | null;
    system_prompt: string;
    activo?: boolean | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    selected_llm_model_id?: string | null;
    db_schema?: boolean | null;
    temperature?: number | null;
}
export interface AgentesCompaneros {
    agente_id: string;
    companero_id: string;
    created_at: Date | null;
    id: string;
}
export interface AgentesCompanerosInput {
    agente_id: string;
    companero_id: string;
    created_at?: Date | null;
    id?: string;
}
export interface AgentesSopsYGuias {
    id: string;
    agente_id: string;
    sop_guia_id: string;
    created_at: Date | null;
}
export interface AgentesSopsYGuiasInput {
    id?: string;
    agente_id: string;
    sop_guia_id: string;
    created_at?: Date | null;
}
export interface AgentesTareas {
    id: string;
    agente_id: string;
    descripcion: string;
    input_data: Json | null;
    estado: task_status;
    resultado: Json | null;
    prioridad: number | null;
    tarea_origen_id: string | null;
    tarea_origen_tipo: string | null;
    creado_por_tipo: string;
    creado_por_id: string;
    iniciado_en: Date | null;
    finalizado_en: Date | null;
    created_at: Date;
    updated_at: Date;
    langgraph_thread_id: string | null;
    titulo: string;
    error_mensaje: string | null;
    error_detalles: Json | null;
}
export interface AgentesTareasInput {
    id?: string;
    agente_id: string;
    descripcion: string;
    input_data?: Json | null;
    estado?: task_status;
    resultado?: Json | null;
    prioridad?: number | null;
    tarea_origen_id?: string | null;
    tarea_origen_tipo?: string | null;
    creado_por_tipo: string;
    creado_por_id: string;
    iniciado_en?: Date | null;
    finalizado_en?: Date | null;
    created_at?: Date;
    updated_at?: Date;
    langgraph_thread_id?: string | null;
    titulo: string;
    error_mensaje?: string | null;
    error_detalles?: Json | null;
}
export interface AgentesTools {
    agente_id: string;
    tool_id: string;
    created_at: Date | null;
    id: string;
}
export interface AgentesToolsInput {
    agente_id: string;
    tool_id: string;
    created_at?: Date | null;
    id?: string;
}
export interface ChatPerformanceMetrics {
    thread_id: number | null;
    total_messages: number | null;
    user_messages: number | null;
    agent_answers: number | null;
    intermediate_steps: number | null;
    last_activity: Date | null;
    first_activity: Date | null;
    conversation_duration_hours: number | null;
    avg_message_length: number | null;
    total_content_length: number | null;
}
export interface ChatPerformanceMetricsInput {
    thread_id?: number | null;
    total_messages?: number | null;
    user_messages?: number | null;
    agent_answers?: number | null;
    intermediate_steps?: number | null;
    last_activity?: Date | null;
    first_activity?: Date | null;
    conversation_duration_hours?: number | null;
    avg_message_length?: number | null;
    total_content_length?: number | null;
}
export interface Comunicaciones {
    id: string;
    tipo: string;
    fecha_hora: Date | null;
    direccion: string | null;
    asunto: string | null;
    contenido: string | null;
    transcripcion_url: string | null;
    duracion_minutos: number | null;
    participante_usuario_id: string | null;
    participante_persona_id: string | null;
    participante_externo_detalle: string | null;
    relacionado_oportunidad_id: string | null;
    relacionado_empresa_id: string | null;
    relacionado_proyecto_id: string | null;
    relacionado_tarea_id: string | null;
    fuente_sistema: string | null;
    created_at: Date | null;
}
export interface ComunicacionesInput {
    id?: string;
    tipo: string;
    fecha_hora?: Date | null;
    direccion?: string | null;
    asunto?: string | null;
    contenido?: string | null;
    transcripcion_url?: string | null;
    duracion_minutos?: number | null;
    participante_usuario_id?: string | null;
    participante_persona_id?: string | null;
    participante_externo_detalle?: string | null;
    relacionado_oportunidad_id?: string | null;
    relacionado_empresa_id?: string | null;
    relacionado_proyecto_id?: string | null;
    relacionado_tarea_id?: string | null;
    fuente_sistema?: string | null;
    created_at?: Date | null;
}
export interface Configuracion {
    id: string;
    clave: string;
    valor: string;
    descripcion: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface ConfiguracionInput {
    id?: string;
    clave: string;
    valor: string;
    descripcion?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface Contratos {
    id: string;
    codigo: string;
    titulo: string;
    empresa_id: string;
    proyecto_id: string | null;
    fecha_inicio: Date;
    fecha_fin: Date | null;
    renovacion_automatica: boolean | null;
    periodo_renovacion: string | null;
    valor_contrato: number | null;
    moneda: string | null;
    periodicidad_facturacion: string | null;
    dia_facturacion: number | null;
    estado: estado_contrato;
    fecha_firma: Date | null;
    firmado_cliente: boolean | null;
    firmado_empresa: boolean | null;
    url_documento: string | null;
    persona_firma_id: string | null;
    terminos_condiciones: string | null;
    clausulas: Json | null;
    fecha_cancelacion: Date | null;
    motivo_cancelacion: string | null;
    contrato_relacionado_id: string | null;
    info_adicional: string | null;
    creado_por: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface ContratosInput {
    id?: string;
    codigo: string;
    titulo: string;
    empresa_id: string;
    proyecto_id?: string | null;
    fecha_inicio: Date;
    fecha_fin?: Date | null;
    renovacion_automatica?: boolean | null;
    periodo_renovacion?: string | null;
    valor_contrato?: number | null;
    moneda?: string | null;
    periodicidad_facturacion?: string | null;
    dia_facturacion?: number | null;
    estado?: estado_contrato;
    fecha_firma?: Date | null;
    firmado_cliente?: boolean | null;
    firmado_empresa?: boolean | null;
    url_documento?: string | null;
    persona_firma_id?: string | null;
    terminos_condiciones?: string | null;
    clausulas?: Json | null;
    fecha_cancelacion?: Date | null;
    motivo_cancelacion?: string | null;
    contrato_relacionado_id?: string | null;
    info_adicional?: string | null;
    creado_por?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Departamentos {
    id: string;
    empresa_id: string;
    nombre: string;
    descripcion: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    info_adicional: string | null;
}
export interface DepartamentosInput {
    id?: string;
    empresa_id: string;
    nombre: string;
    descripcion?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    info_adicional?: string | null;
}
export interface Diagnosticos {
    id: string;
    empresa_id: string | null;
    titulo: string | null;
    fecha_generacion: Date | null;
    diagrama_reactflow: string | null;
    plan_aceleracion_html: string | null;
    estado: string | null;
    version: number | null;
    creado_por: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    diagrama_proceso_json: Json | null;
}
export interface DiagnosticosInput {
    id?: string;
    empresa_id?: string | null;
    titulo?: string | null;
    fecha_generacion?: Date | null;
    diagrama_reactflow?: string | null;
    plan_aceleracion_html?: string | null;
    estado?: string | null;
    version?: number | null;
    creado_por?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    diagrama_proceso_json?: Json | null;
}
export interface DocExports {
    id: string;
    threads_row_id: string;
    user_id: string;
    status: string;
    doc_url: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface DocExportsInput {
    id?: string;
    threads_row_id: string;
    user_id: string;
    status: string;
    doc_url?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface DocumentosEmpresa {
    id: string;
    nombre_documento: string | null;
    url_documento: string | null;
    ruta_carpeta_drive: string | null;
    resumen: string | null;
    usuario_documento_id: string | null;
    creado_por_agente_id: string | null;
    empresa_relacionada_id: string | null;
    proyecto_relacionado_id: string | null;
    proceso_relacionado_id: string | null;
    info_adicional: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface DocumentosEmpresaInput {
    id?: string;
    nombre_documento?: string | null;
    url_documento?: string | null;
    ruta_carpeta_drive?: string | null;
    resumen?: string | null;
    usuario_documento_id?: string | null;
    creado_por_agente_id?: string | null;
    empresa_relacionada_id?: string | null;
    proyecto_relacionado_id?: string | null;
    proceso_relacionado_id?: string | null;
    info_adicional?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Empresas {
    id: string;
    nombre: string;
    sector: string | null;
    logo_url: string | null;
    direccion: string | null;
    telefono: string | null;
    email_principal: string | null;
    website: string | null;
    fecha_alta: Date | null;
    activo: boolean | null;
    created_at: Date | null;
    updated_at: Date | null;
    info_adicional: string | null;
    nif_cif: string | null;
    direccion_fiscal: string | null;
    tipo_empresa: string | null;
    descripcion: string | null;
    tipo_relacion: tipo_relacion_empresa_enum | null;
}
export interface EmpresasInput {
    id?: string;
    nombre: string;
    sector?: string | null;
    logo_url?: string | null;
    direccion?: string | null;
    telefono?: string | null;
    email_principal?: string | null;
    website?: string | null;
    fecha_alta?: Date | null;
    activo?: boolean | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    info_adicional?: string | null;
    nif_cif?: string | null;
    direccion_fiscal?: string | null;
    tipo_empresa?: string | null;
    descripcion?: string | null;
    tipo_relacion?: tipo_relacion_empresa_enum | null;
}
export interface Etiquetas {
    id: string;
    nombre: string;
    color: string | null;
    descripcion: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface EtiquetasInput {
    id?: string;
    nombre: string;
    color?: string | null;
    descripcion?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Evaluaciones {
    id: number;
    created_at: Date;
    nombre_agente_workflow: string | null;
    proposito_agente: string | null;
    criterios_adicionales_evaluacion: string | null;
    agente_output: string | null;
    puntuacion: number | null;
    argumentos_puntuacion: string | null;
    sugerencias_mejora: string | null;
    execution_id: string | null;
    n8n_workflow_id: string | null;
    estado: evaluaciones_estado_enum | null;
    agente_id: string | null;
    nombre_agente_amigable: string | null;
    mejora_agente_id: string | null;
    user_id: string | null;
}
export interface EvaluacionesInput {
    id: number;
    created_at?: Date;
    nombre_agente_workflow?: string | null;
    proposito_agente?: string | null;
    criterios_adicionales_evaluacion?: string | null;
    agente_output?: string | null;
    puntuacion?: number | null;
    argumentos_puntuacion?: string | null;
    sugerencias_mejora?: string | null;
    execution_id?: string | null;
    n8n_workflow_id?: string | null;
    estado?: evaluaciones_estado_enum | null;
    agente_id?: string | null;
    nombre_agente_amigable?: string | null;
    mejora_agente_id?: string | null;
    user_id?: string | null;
}
export interface Facturas {
    id: string;
    numero_factura: string;
    serie: string | null;
    empresa_id: string;
    proyecto_id: string | null;
    contrato_id: string | null;
    fecha_emision: Date;
    fecha_vencimiento: Date;
    subtotal: number;
    iva: number;
    total: number;
    estado: estado_factura;
    fecha_pago: Date | null;
    metodo_pago: string | null;
    referencia_pago: string | null;
    pdf_url: string | null;
    xml_url: string | null;
    plantilla_id: string | null;
    emisor_nombre: string;
    emisor_nif: string;
    emisor_direccion: string;
    receptor_nombre: string;
    receptor_nif: string;
    receptor_direccion: string;
    items: Json;
    recordatorios_enviados: number | null;
    fecha_ultimo_recordatorio: Date | null;
    info_adicional: string | null;
    creado_por: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface FacturasInput {
    id?: string;
    numero_factura: string;
    serie?: string | null;
    empresa_id: string;
    proyecto_id?: string | null;
    contrato_id?: string | null;
    fecha_emision?: Date;
    fecha_vencimiento: Date;
    subtotal: number;
    iva: number;
    total: number;
    estado?: estado_factura;
    fecha_pago?: Date | null;
    metodo_pago?: string | null;
    referencia_pago?: string | null;
    pdf_url?: string | null;
    xml_url?: string | null;
    plantilla_id?: string | null;
    emisor_nombre: string;
    emisor_nif: string;
    emisor_direccion: string;
    receptor_nombre: string;
    receptor_nif: string;
    receptor_direccion: string;
    items?: Json;
    recordatorios_enviados?: number | null;
    fecha_ultimo_recordatorio?: Date | null;
    info_adicional?: string | null;
    creado_por?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Grabaciones {
    id: string;
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: grabacion_entidad_tipo;
    usuario_id: string;
    url_almacenamiento: string;
    estado_procesamiento: grabacion_estado_procesamiento;
    tipo_grabacion: grabacion_tipo;
    transcripcion: string | null;
    duracion_segundos: number | null;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface GrabacionesInput {
    id?: string;
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: grabacion_entidad_tipo;
    usuario_id: string;
    url_almacenamiento: string;
    estado_procesamiento?: grabacion_estado_procesamiento;
    tipo_grabacion: grabacion_tipo;
    transcripcion?: string | null;
    duracion_segundos?: number | null;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface HallazgosClientes {
    id: string;
    reunion_id: string;
    empresa_id: string;
    persona_id: string | null;
    tipo: tipo_hallazgo_enum | null;
    descripcion: string | null;
    impacto: string | null;
    posible_solucion: string | null;
    estado: estado_hallazgo_enum | null;
    created_at: Date | null;
    updated_at: Date | null;
    procesos_relacionados: Json | null;
    titulo: string;
}
export interface HallazgosClientesInput {
    id?: string;
    reunion_id: string;
    empresa_id: string;
    persona_id?: string | null;
    tipo?: tipo_hallazgo_enum | null;
    descripcion?: string | null;
    impacto?: string | null;
    posible_solucion?: string | null;
    estado?: estado_hallazgo_enum | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    procesos_relacionados?: Json | null;
    titulo: string;
}
export interface HallazgosReunionesDepartamentos {
    hallazgo_reunion_id: string;
    departamento_id: string;
    created_at: Date;
}
export interface HallazgosReunionesDepartamentosInput {
    hallazgo_reunion_id: string;
    departamento_id: string;
    created_at?: Date;
}
export interface HumanInterventionRequests {
    id: string;
    langgraph_thread_id: string;
    asistente_id: string;
    request_type: string;
    request_details: Json | null;
    status: string;
    resolution_data: Json | null;
    requested_at: Date;
    resolved_at: Date | null;
    resolved_by: string | null;
    created_at: Date;
    updated_at: Date;
    triggering_tool_call_id: string | null;
}
export interface HumanInterventionRequestsInput {
    id?: string;
    langgraph_thread_id: string;
    asistente_id: string;
    request_type: string;
    request_details?: Json | null;
    status?: string;
    resolution_data?: Json | null;
    requested_at?: Date;
    resolved_at?: Date | null;
    resolved_by?: string | null;
    created_at?: Date;
    updated_at?: Date;
    triggering_tool_call_id?: string | null;
}
export interface Ideas {
    id: string;
    titulo: string;
    descripcion: string | null;
    empresa_relacionada_id: string | null;
    proyecto_relacionado_id: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    estado: enum_ideas_estado | null;
    prioridad: enum_ideas_prioridad | null;
}
export interface IdeasInput {
    id?: string;
    titulo: string;
    descripcion?: string | null;
    empresa_relacionada_id?: string | null;
    proyecto_relacionado_id?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    estado?: enum_ideas_estado | null;
    prioridad?: enum_ideas_prioridad | null;
}
export interface LlmModels {
    id: string;
    provider_id: string;
    model_name: string;
    description: string | null;
    context_window: number | null;
    active: boolean;
    created_at: Date;
    input_cost_per_million_tokens: number | null;
    output_cost_per_million_tokens: number | null;
}
export interface LlmModelsInput {
    id?: string;
    provider_id: string;
    model_name: string;
    description?: string | null;
    context_window?: number | null;
    active?: boolean;
    created_at?: Date;
    input_cost_per_million_tokens?: number | null;
    output_cost_per_million_tokens?: number | null;
}
export interface LlmProviders {
    id: string;
    provider_key: string;
    provider_name: string;
    created_at: Date;
}
export interface LlmProvidersInput {
    id?: string;
    provider_key: string;
    provider_name: string;
    created_at?: Date;
}
export interface LogsSistema {
    id: string;
    tipo: string;
    origen: string;
    mensaje: string;
    detalles: Json | null;
    usuario_id: string | null;
    timestamp: Date | null;
}
export interface LogsSistemaInput {
    id?: string;
    tipo: string;
    origen: string;
    mensaje: string;
    detalles?: Json | null;
    usuario_id?: string | null;
    timestamp?: Date | null;
}
export interface MejorasAgentes {
    id: string;
    agente_id: string | null;
    n8n_workflow_id: string | null;
    explicacion_mejoras: string;
    estado: estado_mejora;
    created_at: Date;
    updated_at: Date | null;
    system_prompt_original: string | null;
    user_prompt_original: string | null;
    system_prompt_mejorado: string | null;
    user_prompt_mejorado: string | null;
    set_node_id: string | null;
    nombre_agente_amigable: string | null;
    nombre_workflow: string | null;
    user_id: string;
}
export interface MejorasAgentesInput {
    id?: string;
    agente_id?: string | null;
    n8n_workflow_id?: string | null;
    explicacion_mejoras: string;
    estado?: estado_mejora;
    created_at?: Date;
    updated_at?: Date | null;
    system_prompt_original?: string | null;
    user_prompt_original?: string | null;
    system_prompt_mejorado?: string | null;
    user_prompt_mejorado?: string | null;
    set_node_id?: string | null;
    nombre_agente_amigable?: string | null;
    nombre_workflow?: string | null;
    user_id: string;
}
export interface Notificaciones {
    id: string;
    usuario_id: string;
    titulo: string;
    mensaje: string;
    url_destino: string | null;
    estado: string;
    tipo_notificacion: string;
    created_at: Date;
}
export interface NotificacionesInput {
    id?: string;
    usuario_id: string;
    titulo: string;
    mensaje: string;
    url_destino?: string | null;
    estado?: string;
    tipo_notificacion: string;
    created_at?: Date;
}
export interface Oportunidades {
    id: string;
    titulo: string;
    descripcion: string | null;
    empresa_id: string | null;
    persona_contacto_id: string | null;
    valor_estimado: number | null;
    moneda: string | null;
    etapa: string | null;
    probabilidad_cierre: number | null;
    fecha_cierre_estimada: Date | null;
    fecha_cierre_real: Date | null;
    motivo_perdida: string | null;
    documento_url: string | null;
    asignado_usuario_id: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface OportunidadesInput {
    id?: string;
    titulo: string;
    descripcion?: string | null;
    empresa_id?: string | null;
    persona_contacto_id?: string | null;
    valor_estimado?: number | null;
    moneda?: string | null;
    etapa?: string | null;
    probabilidad_cierre?: number | null;
    fecha_cierre_estimada?: Date | null;
    fecha_cierre_real?: Date | null;
    motivo_perdida?: string | null;
    documento_url?: string | null;
    asignado_usuario_id?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Personas {
    id: string;
    empresa_id: string | null;
    departamento_id: string | null;
    nombre: string;
    apellidos: string | null;
    email: string | null;
    telefono: string | null;
    cargo: string | null;
    tipo: string[];
    es_decision_maker: boolean | null;
    entrevistado: boolean | null;
    fecha_entrevista: Date | null;
    fecha_alta: Date | null;
    fecha_baja: Date | null;
    activo: boolean | null;
    info_adicional: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    responsable_departamento: boolean | null;
    linkedin_url: string | null;
    usuario_id: string | null;
}
export interface PersonasInput {
    id?: string;
    empresa_id?: string | null;
    departamento_id?: string | null;
    nombre: string;
    apellidos?: string | null;
    email?: string | null;
    telefono?: string | null;
    cargo?: string | null;
    tipo: string[];
    es_decision_maker?: boolean | null;
    entrevistado?: boolean | null;
    fecha_entrevista?: Date | null;
    fecha_alta?: Date | null;
    fecha_baja?: Date | null;
    activo?: boolean | null;
    info_adicional?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    responsable_departamento?: boolean | null;
    linkedin_url?: string | null;
    usuario_id?: string | null;
}
export interface PlantillasTareas {
    id: string;
    titulo_plantilla: string;
    descripcion_base: string | null;
    duracion_estimada_horas: number | null;
    prioridad_predeterminada: prioridad_general_enum | null;
    info_adicional_plantilla: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface PlantillasTareasInput {
    id?: string;
    titulo_plantilla: string;
    descripcion_base?: string | null;
    duracion_estimada_horas?: number | null;
    prioridad_predeterminada?: prioridad_general_enum | null;
    info_adicional_plantilla?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface Preguntas {
    id: string;
    titulo: string;
    estado: pregunta_estado;
    visibilidad: pregunta_visibilidad;
    destinatario_usuario_id: string | null;
    origen_reunion_id: string | null;
    origen_proceso_id: string | null;
    respuesta_texto: string | null;
    respuesta_grabacion_id: string | null;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface PreguntasInput {
    id?: string;
    titulo: string;
    estado?: pregunta_estado;
    visibilidad?: pregunta_visibilidad;
    destinatario_usuario_id?: string | null;
    origen_reunion_id?: string | null;
    origen_proceso_id?: string | null;
    respuesta_texto?: string | null;
    respuesta_grabacion_id?: string | null;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface Procesos {
    id: string;
    empresa_id: string;
    departamento_id: string | null;
    nombre: string;
    descripcion: string | null;
    es_repetitivo: boolean | null;
    es_cuello_botella: boolean | null;
    es_manual: boolean | null;
    valor_negocio: string | null;
    complejidad_automatizacion: string | null;
    prioridad_automatizacion: string | null;
    tiempo_estimado_manual: number | null;
    frecuencia: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    info_adicional: string | null;
    persona_id: string | null;
    herramientas_utilizadas: Json | null;
    tipo_proceso: tipo_proceso_enum | null;
    proceso_plantilla_origen_id: string | null;
    reunion_origen_id: string | null;
}
export interface ProcesosInput {
    id?: string;
    empresa_id: string;
    departamento_id?: string | null;
    nombre: string;
    descripcion?: string | null;
    es_repetitivo?: boolean | null;
    es_cuello_botella?: boolean | null;
    es_manual?: boolean | null;
    valor_negocio?: string | null;
    complejidad_automatizacion?: string | null;
    prioridad_automatizacion?: string | null;
    tiempo_estimado_manual?: number | null;
    frecuencia?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    info_adicional?: string | null;
    persona_id?: string | null;
    herramientas_utilizadas?: Json | null;
    tipo_proceso?: tipo_proceso_enum | null;
    proceso_plantilla_origen_id?: string | null;
    reunion_origen_id?: string | null;
}
export interface ProcesosClientes {
    id: string;
    empresa_cliente_id: string;
    nombre: string;
    descripcion: string | null;
    estado_analisis: estado_proceso_cliente_enum | null;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
    reunion_origen_id: string | null;
    es_repetitivo: boolean | null;
    es_cuello_botella: boolean | null;
    es_manual: boolean | null;
    valor_negocio_cliente: string | null;
    complejidad_automatizacion_aceleralia: tipo_complejidad_automatizacion | null;
    prioridad_automatizacion_aceleralia: tipo_prioridad_automatizacion | null;
    duracion_minutos_por_ejecucion: number | null;
    frecuencia_periodo: tipo_frecuencia_periodo | null;
    herramientas_utilizadas_cliente: Json | null;
    frecuencia_ocurrencias: number | null;
}
export interface ProcesosClientesInput {
    id?: string;
    empresa_cliente_id: string;
    nombre: string;
    descripcion?: string | null;
    estado_analisis?: estado_proceso_cliente_enum | null;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
    reunion_origen_id?: string | null;
    es_repetitivo?: boolean | null;
    es_cuello_botella?: boolean | null;
    es_manual?: boolean | null;
    valor_negocio_cliente?: string | null;
    complejidad_automatizacion_aceleralia?: tipo_complejidad_automatizacion | null;
    prioridad_automatizacion_aceleralia?: tipo_prioridad_automatizacion | null;
    duracion_minutos_por_ejecucion?: number | null;
    frecuencia_periodo?: tipo_frecuencia_periodo | null;
    herramientas_utilizadas_cliente?: Json | null;
    frecuencia_ocurrencias?: number | null;
}
export interface ProcesosClientesDepartamentos {
    proceso_cliente_id: string;
    departamento_id: string;
    created_at: Date;
}
export interface ProcesosClientesDepartamentosInput {
    proceso_cliente_id: string;
    departamento_id: string;
    created_at?: Date;
}
export interface ProcesosClientesResponsables {
    id: string;
    proceso_cliente_id: string;
    persona_cliente_id: string;
    created_at: Date;
    updated_at: Date;
}
export interface ProcesosClientesResponsablesInput {
    id?: string;
    proceso_cliente_id: string;
    persona_cliente_id: string;
    created_at?: Date;
    updated_at?: Date;
}
export interface ProcesosPlantillas {
    id: string;
    nombre_plantilla: string;
    descripcion_plantilla: string | null;
    objetivo_plantilla: string | null;
    info_adicional_plantilla: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface ProcesosPlantillasInput {
    id?: string;
    nombre_plantilla: string;
    descripcion_plantilla?: string | null;
    objetivo_plantilla?: string | null;
    info_adicional_plantilla?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface ProcesosTareasPlantilla {
    id: string;
    proceso_plantilla_id: string;
    plantilla_tarea_id: string;
    orden_en_proceso: number;
    dias_desplazamiento: number | null;
    es_obligatoria: boolean | null;
    notas_especificas_proceso: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface ProcesosTareasPlantillaInput {
    id?: string;
    proceso_plantilla_id: string;
    plantilla_tarea_id: string;
    orden_en_proceso: number;
    dias_desplazamiento?: number | null;
    es_obligatoria?: boolean | null;
    notas_especificas_proceso?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface ProyectoPersonas {
    id: string;
    proyecto_id: string | null;
    persona_id: string | null;
    rol: string | null;
    asignado_desde: Date | null;
    asignado_hasta: Date | null;
    porcentaje_dedicacion: number | null;
    es_responsable: boolean | null;
    info_adicional: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface ProyectoPersonasInput {
    id?: string;
    proyecto_id?: string | null;
    persona_id?: string | null;
    rol?: string | null;
    asignado_desde?: Date | null;
    asignado_hasta?: Date | null;
    porcentaje_dedicacion?: number | null;
    es_responsable?: boolean | null;
    info_adicional?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Proyectos {
    id: string;
    nombre: string;
    descripcion: string | null;
    objetivo: string | null;
    estado: string;
    fecha_inicio: Date | null;
    fecha_fin_estimada: Date | null;
    fecha_fin_real: Date | null;
    presupuesto: number | null;
    created_at: Date | null;
    updated_at: Date | null;
    responsable_persona_id: string | null;
    info_adicional: string | null;
    prioridad: string | null;
    progreso: number | null;
    responsable_usuario_id: string | null;
}
export interface ProyectosInput {
    id?: string;
    nombre: string;
    descripcion?: string | null;
    objetivo?: string | null;
    estado: string;
    fecha_inicio?: Date | null;
    fecha_fin_estimada?: Date | null;
    fecha_fin_real?: Date | null;
    presupuesto?: number | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    responsable_persona_id?: string | null;
    info_adicional?: string | null;
    prioridad?: string | null;
    progreso?: number | null;
    responsable_usuario_id?: string | null;
}
export interface ProyectosEmpresas {
    proyecto_id: string;
    empresa_id: string;
}
export interface ProyectosEmpresasInput {
    proyecto_id: string;
    empresa_id: string;
}
export interface ProyectosProcesos {
    id: string;
    proyecto_id: string;
    proceso_id: string;
    created_at: Date;
}
export interface ProyectosProcesosInput {
    id?: string;
    proyecto_id: string;
    proceso_id: string;
    created_at?: Date;
}
export interface RetosSubtareas {
    id: string;
    reto_usuario_id: string;
    titulo: string;
    descripcion: string | null;
    tipo_accion: reto_tipo_accion;
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: grabacion_entidad_tipo;
    estado: reto_estado;
    created_at: Date;
    updated_at: Date;
}
export interface RetosSubtareasInput {
    id?: string;
    reto_usuario_id: string;
    titulo: string;
    descripcion?: string | null;
    tipo_accion: reto_tipo_accion;
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: grabacion_entidad_tipo;
    estado?: reto_estado;
    created_at?: Date;
    updated_at?: Date;
}
export interface RetosUsuarios {
    id: string;
    usuario_id: string;
    titulo: string;
    descripcion: string | null;
    puntos_recompensa: number;
    estado: reto_estado;
    prioridad: number | null;
    url_destino: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface RetosUsuariosInput {
    id?: string;
    usuario_id: string;
    titulo: string;
    descripcion?: string | null;
    puntos_recompensa?: number;
    estado?: reto_estado;
    prioridad?: number | null;
    url_destino?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface ReunionEmpresasAsociadas {
    reunion_id: string;
    empresa_id: string;
    created_at: Date;
}
export interface ReunionEmpresasAsociadasInput {
    reunion_id: string;
    empresa_id: string;
    created_at?: Date;
}
export interface ReunionPersonasAsociadas {
    reunion_id: string;
    persona_id: string;
    created_at: Date;
}
export interface ReunionPersonasAsociadasInput {
    reunion_id: string;
    persona_id: string;
    created_at?: Date;
}
export interface ReunionSpeakerAsignaciones {
    id: string;
    reunion_id: string;
    speaker_tag: string;
    asignado_a_tipo: string;
    asignado_a_id: string;
    nombre_asignado: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface ReunionSpeakerAsignacionesInput {
    id?: string;
    reunion_id: string;
    speaker_tag: string;
    asignado_a_tipo: string;
    asignado_a_id: string;
    nombre_asignado?: string | null;
    created_at?: Date;
    updated_at?: Date;
}
export interface Reuniones {
    id: string;
    user_id: string | null;
    titulo: string | null;
    observaciones_iniciales: string | null;
    url_grabacion_original: string | null;
    url_grabacion_publica: string | null;
    fecha_reunion: Date | null;
    transcripcion_raw: string | null;
    transcripcion_final: string | null;
    resumen: string | null;
    puntos_clave: Json | null;
    estado_procesamiento: string;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
    entrevista: boolean | null;
    video: boolean | null;
    duracion_minutos: number | null;
}
export interface ReunionesInput {
    id?: string;
    user_id?: string | null;
    titulo?: string | null;
    observaciones_iniciales?: string | null;
    url_grabacion_original?: string | null;
    url_grabacion_publica?: string | null;
    fecha_reunion?: Date | null;
    transcripcion_raw?: string | null;
    transcripcion_final?: string | null;
    resumen?: string | null;
    puntos_clave?: Json | null;
    estado_procesamiento?: string;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
    entrevista?: boolean | null;
    video?: boolean | null;
    duracion_minutos?: number | null;
}
export interface SopsYGuias {
    id: string;
    titulo: string;
    descripcion: string | null;
    contenido_markdown: string;
    dueno_sop_id: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    type: sop_guia_type_enum | null;
}
export interface SopsYGuiasInput {
    id?: string;
    titulo: string;
    descripcion?: string | null;
    contenido_markdown: string;
    dueno_sop_id?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    type?: sop_guia_type_enum | null;
}
export interface Tareas {
    id: string;
    proyecto_id: string;
    workflow_id: string | null;
    titulo: string;
    descripcion: string | null;
    fecha_vencimiento: Date | null;
    fecha_completado: Date | null;
    asignado_a: string | null;
    creado_por: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    info_adicional: string | null;
    estado: estado_tarea;
    prioridad: prioridad_tarea | null;
    tarea_padre_id: string | null;
    urgencia: urgencia_tarea_enum | null;
    reunion_id: string | null;
    fecha_inicio: Date | null;
}
export interface TareasInput {
    id?: string;
    proyecto_id: string;
    workflow_id?: string | null;
    titulo: string;
    descripcion?: string | null;
    fecha_vencimiento?: Date | null;
    fecha_completado?: Date | null;
    asignado_a?: string | null;
    creado_por?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    info_adicional?: string | null;
    estado?: estado_tarea;
    prioridad?: prioridad_tarea | null;
    tarea_padre_id?: string | null;
    urgencia?: urgencia_tarea_enum | null;
    reunion_id?: string | null;
    fecha_inicio?: Date | null;
}
export interface TareasClientes {
    id: string;
    proceso_cliente_id: string;
    nombre_tarea_cliente: string;
    descripcion_tarea_cliente: string | null;
    duracion_minutos_por_ejecucion: number | null;
    frecuencia_periodo: tipo_frecuencia_periodo | null;
    es_manual_cliente: boolean | null;
    herramientas_utilizadas_cliente: Json | null;
    puntos_dolor_cliente: string | null;
    oportunidades_mejora_cliente: string | null;
    info_adicional: string | null;
    created_at: Date;
    updated_at: Date;
    frecuencia_ocurrencias: number | null;
}
export interface TareasClientesInput {
    id?: string;
    proceso_cliente_id: string;
    nombre_tarea_cliente: string;
    descripcion_tarea_cliente?: string | null;
    duracion_minutos_por_ejecucion?: number | null;
    frecuencia_periodo?: tipo_frecuencia_periodo | null;
    es_manual_cliente?: boolean | null;
    herramientas_utilizadas_cliente?: Json | null;
    puntos_dolor_cliente?: string | null;
    oportunidades_mejora_cliente?: string | null;
    info_adicional?: string | null;
    created_at?: Date;
    updated_at?: Date;
    frecuencia_ocurrencias?: number | null;
}
export interface TareasClientesResponsables {
    id: string;
    tarea_cliente_id: string;
    persona_cliente_id: string;
    created_at: Date;
    updated_at: Date;
}
export interface TareasClientesResponsablesInput {
    id?: string;
    tarea_cliente_id: string;
    persona_cliente_id: string;
    created_at?: Date;
    updated_at?: Date;
}
export interface TareasEmpresas {
    tarea_id: string;
    empresa_id: string;
}
export interface TareasEmpresasInput {
    tarea_id: string;
    empresa_id: string;
}
export interface TareasEtiquetas {
    tarea_id: string;
    etiqueta_id: string;
}
export interface TareasEtiquetasInput {
    tarea_id: string;
    etiqueta_id: string;
}
export interface ThreadMessageCounts {
    thread_id: number | null;
    message_count: number | null;
    last_message_at: Date | null;
    first_message_at: Date | null;
}
export interface ThreadMessageCountsInput {
    thread_id?: number | null;
    message_count?: number | null;
    last_message_at?: Date | null;
    first_message_at?: Date | null;
}
export interface Threads {
    thread_id: number;
    created_at: Date;
    content: string | null;
    type: string | null;
    from: string | null;
    message_id: number | null;
    agent_id: string | null;
    user_id: string | null;
    id: string;
    request_type: thread_request_type_enum | null;
    input_token_cost: number | null;
    output_token_cost: number | null;
}
export interface ThreadsInput {
    thread_id: number;
    created_at?: Date;
    content?: string | null;
    type?: string | null;
    from?: string | null;
    message_id?: number | null;
    agent_id?: string | null;
    user_id?: string | null;
    id?: string;
    request_type?: thread_request_type_enum | null;
    input_token_cost?: number | null;
    output_token_cost?: number | null;
}
export interface ThreadsMetadata {
    thread_id: number;
    titulo: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface ThreadsMetadataInput {
    thread_id: number;
    titulo?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface ThreadsResumen {
    id: string;
    thread_id_procesado: string;
    titulo: string | null;
    resumen: string | null;
    tools_utilizadas: string | null;
    observaciones_agente: string | null;
    fecha_resumen: Date | null;
    db_modifications_summary: string | null;
}
export interface ThreadsResumenInput {
    id?: string;
    thread_id_procesado: string;
    titulo?: string | null;
    resumen?: string | null;
    tools_utilizadas?: string | null;
    observaciones_agente?: string | null;
    fecha_resumen?: Date | null;
    db_modifications_summary?: string | null;
}
export interface TicketMensajes {
    id: string;
    ticket_id: string;
    usuario_id: string | null;
    contenido: string;
    tipo: string;
    es_privado: boolean | null;
    adjunto_url: string | null;
    created_at: Date | null;
}
export interface TicketMensajesInput {
    id?: string;
    ticket_id: string;
    usuario_id?: string | null;
    contenido: string;
    tipo: string;
    es_privado?: boolean | null;
    adjunto_url?: string | null;
    created_at?: Date | null;
}
export interface Tickets {
    id: string;
    empresa_id: string;
    proyecto_id: string | null;
    workflow_id: string | null;
    contacto_id: string | null;
    titulo: string;
    descripcion: string | null;
    detalles_tecnicos: string | null;
    reproducibilidad: string | null;
    estado: string;
    prioridad: string;
    fecha_resolucion: Date | null;
    asignado_a: string | null;
    resolucion_descripcion: string | null;
    canal_origen: string | null;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface TicketsInput {
    id?: string;
    empresa_id: string;
    proyecto_id?: string | null;
    workflow_id?: string | null;
    contacto_id?: string | null;
    titulo: string;
    descripcion?: string | null;
    detalles_tecnicos?: string | null;
    reproducibilidad?: string | null;
    estado: string;
    prioridad: string;
    fecha_resolucion?: Date | null;
    asignado_a?: string | null;
    resolucion_descripcion?: string | null;
    canal_origen?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Tools {
    id: string;
    tool_name: string;
    tool_description: string | null;
    tool_config: string;
    created_at: Date | null;
    updated_at: Date | null;
}
export interface ToolsInput {
    id?: string;
    tool_name: string;
    tool_description?: string | null;
    tool_config: string;
    created_at?: Date | null;
    updated_at?: Date | null;
}
export interface Usuarios {
    id: string;
    email: string;
    nombre: string;
    rol: string;
    empresa_id: string | null;
    avatar_url: string | null;
    ultimo_acceso: Date | null;
    created_at: Date | null;
    actualizado_en: Date | null;
    info_adicional: string | null;
    apellidos: string | null;
    auth_user_id: string;
    puntos: number;
    racha_actual: number | null;
    ultima_actividad_racha: Date | null;
}
export interface UsuariosInput {
    id?: string;
    email: string;
    nombre: string;
    rol: string;
    empresa_id?: string | null;
    avatar_url?: string | null;
    ultimo_acceso?: Date | null;
    created_at?: Date | null;
    actualizado_en?: Date | null;
    info_adicional?: string | null;
    apellidos?: string | null;
    auth_user_id: string;
    puntos?: number;
    racha_actual?: number | null;
    ultima_actividad_racha?: Date | null;
}
export interface WorkflowErrores {
    id: string;
    workflow_id: string | null;
    tipo_error: string;
    mensaje: string;
    detalles: Json | null;
    stack_trace: string | null;
    fecha_error: Date | null;
    estado: string;
    notificado: boolean | null;
    resuelto_por: string | null;
    fecha_resolucion: Date | null;
    notas_resolucion: string | null;
    created_at: Date | null;
    updated_at: Date | null;
    n8n_workflow_id: string;
}
export interface WorkflowErroresInput {
    id?: string;
    workflow_id?: string | null;
    tipo_error: string;
    mensaje: string;
    detalles?: Json | null;
    stack_trace?: string | null;
    fecha_error?: Date | null;
    estado: string;
    notificado?: boolean | null;
    resuelto_por?: string | null;
    fecha_resolucion?: Date | null;
    notas_resolucion?: string | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    n8n_workflow_id: string;
}
export interface Workflows {
    id: string;
    n8n_workflow_id: string | null;
    nombre: string;
    descripcion: string | null;
    proyecto_id: string | null;
    json_configuracion: Json | null;
    created_at: Date | null;
    updated_at: Date | null;
    etiquetas: Json | null;
    system_prompt_plantilla: string | null;
    user_prompt_plantilla: string | null;
    system_prompt_mejorado: string | null;
    user_prompt_mejorado: string | null;
}
export interface WorkflowsInput {
    id?: string;
    n8n_workflow_id?: string | null;
    nombre: string;
    descripcion?: string | null;
    proyecto_id?: string | null;
    json_configuracion?: Json | null;
    created_at?: Date | null;
    updated_at?: Date | null;
    etiquetas?: Json | null;
    system_prompt_plantilla?: string | null;
    user_prompt_plantilla?: string | null;
    system_prompt_mejorado?: string | null;
    user_prompt_mejorado?: string | null;
}
export interface Db {
    public: {
        Tables: {
            agentes: {
                Row: Agentes;
                Insert: AgentesInput;
                Update: Partial<AgentesInput>;
            };
            agentes_companeros: {
                Row: AgentesCompaneros;
                Insert: AgentesCompanerosInput;
                Update: Partial<AgentesCompanerosInput>;
            };
            agentes_sops_y_guias: {
                Row: AgentesSopsYGuias;
                Insert: AgentesSopsYGuiasInput;
                Update: Partial<AgentesSopsYGuiasInput>;
            };
            agentes_tareas: {
                Row: AgentesTareas;
                Insert: AgentesTareasInput;
                Update: Partial<AgentesTareasInput>;
            };
            agentes_tools: {
                Row: AgentesTools;
                Insert: AgentesToolsInput;
                Update: Partial<AgentesToolsInput>;
            };
            chat_performance_metrics: {
                Row: ChatPerformanceMetrics;
                Insert: ChatPerformanceMetricsInput;
                Update: Partial<ChatPerformanceMetricsInput>;
            };
            comunicaciones: {
                Row: Comunicaciones;
                Insert: ComunicacionesInput;
                Update: Partial<ComunicacionesInput>;
            };
            configuracion: {
                Row: Configuracion;
                Insert: ConfiguracionInput;
                Update: Partial<ConfiguracionInput>;
            };
            contratos: {
                Row: Contratos;
                Insert: ContratosInput;
                Update: Partial<ContratosInput>;
            };
            departamentos: {
                Row: Departamentos;
                Insert: DepartamentosInput;
                Update: Partial<DepartamentosInput>;
            };
            diagnosticos: {
                Row: Diagnosticos;
                Insert: DiagnosticosInput;
                Update: Partial<DiagnosticosInput>;
            };
            doc_exports: {
                Row: DocExports;
                Insert: DocExportsInput;
                Update: Partial<DocExportsInput>;
            };
            documentos_empresa: {
                Row: DocumentosEmpresa;
                Insert: DocumentosEmpresaInput;
                Update: Partial<DocumentosEmpresaInput>;
            };
            empresas: {
                Row: Empresas;
                Insert: EmpresasInput;
                Update: Partial<EmpresasInput>;
            };
            etiquetas: {
                Row: Etiquetas;
                Insert: EtiquetasInput;
                Update: Partial<EtiquetasInput>;
            };
            evaluaciones: {
                Row: Evaluaciones;
                Insert: EvaluacionesInput;
                Update: Partial<EvaluacionesInput>;
            };
            facturas: {
                Row: Facturas;
                Insert: FacturasInput;
                Update: Partial<FacturasInput>;
            };
            grabaciones: {
                Row: Grabaciones;
                Insert: GrabacionesInput;
                Update: Partial<GrabacionesInput>;
            };
            hallazgos_clientes: {
                Row: HallazgosClientes;
                Insert: HallazgosClientesInput;
                Update: Partial<HallazgosClientesInput>;
            };
            hallazgos_reuniones_departamentos: {
                Row: HallazgosReunionesDepartamentos;
                Insert: HallazgosReunionesDepartamentosInput;
                Update: Partial<HallazgosReunionesDepartamentosInput>;
            };
            human_intervention_requests: {
                Row: HumanInterventionRequests;
                Insert: HumanInterventionRequestsInput;
                Update: Partial<HumanInterventionRequestsInput>;
            };
            ideas: {
                Row: Ideas;
                Insert: IdeasInput;
                Update: Partial<IdeasInput>;
            };
            llm_models: {
                Row: LlmModels;
                Insert: LlmModelsInput;
                Update: Partial<LlmModelsInput>;
            };
            llm_providers: {
                Row: LlmProviders;
                Insert: LlmProvidersInput;
                Update: Partial<LlmProvidersInput>;
            };
            logs_sistema: {
                Row: LogsSistema;
                Insert: LogsSistemaInput;
                Update: Partial<LogsSistemaInput>;
            };
            mejoras_agentes: {
                Row: MejorasAgentes;
                Insert: MejorasAgentesInput;
                Update: Partial<MejorasAgentesInput>;
            };
            notificaciones: {
                Row: Notificaciones;
                Insert: NotificacionesInput;
                Update: Partial<NotificacionesInput>;
            };
            oportunidades: {
                Row: Oportunidades;
                Insert: OportunidadesInput;
                Update: Partial<OportunidadesInput>;
            };
            personas: {
                Row: Personas;
                Insert: PersonasInput;
                Update: Partial<PersonasInput>;
            };
            plantillas_tareas: {
                Row: PlantillasTareas;
                Insert: PlantillasTareasInput;
                Update: Partial<PlantillasTareasInput>;
            };
            preguntas: {
                Row: Preguntas;
                Insert: PreguntasInput;
                Update: Partial<PreguntasInput>;
            };
            procesos: {
                Row: Procesos;
                Insert: ProcesosInput;
                Update: Partial<ProcesosInput>;
            };
            procesos_clientes: {
                Row: ProcesosClientes;
                Insert: ProcesosClientesInput;
                Update: Partial<ProcesosClientesInput>;
            };
            procesos_clientes_departamentos: {
                Row: ProcesosClientesDepartamentos;
                Insert: ProcesosClientesDepartamentosInput;
                Update: Partial<ProcesosClientesDepartamentosInput>;
            };
            procesos_clientes_responsables: {
                Row: ProcesosClientesResponsables;
                Insert: ProcesosClientesResponsablesInput;
                Update: Partial<ProcesosClientesResponsablesInput>;
            };
            procesos_plantillas: {
                Row: ProcesosPlantillas;
                Insert: ProcesosPlantillasInput;
                Update: Partial<ProcesosPlantillasInput>;
            };
            procesos_tareas_plantilla: {
                Row: ProcesosTareasPlantilla;
                Insert: ProcesosTareasPlantillaInput;
                Update: Partial<ProcesosTareasPlantillaInput>;
            };
            proyecto_personas: {
                Row: ProyectoPersonas;
                Insert: ProyectoPersonasInput;
                Update: Partial<ProyectoPersonasInput>;
            };
            proyectos: {
                Row: Proyectos;
                Insert: ProyectosInput;
                Update: Partial<ProyectosInput>;
            };
            proyectos_empresas: {
                Row: ProyectosEmpresas;
                Insert: ProyectosEmpresasInput;
                Update: Partial<ProyectosEmpresasInput>;
            };
            proyectos_procesos: {
                Row: ProyectosProcesos;
                Insert: ProyectosProcesosInput;
                Update: Partial<ProyectosProcesosInput>;
            };
            retos_subtareas: {
                Row: RetosSubtareas;
                Insert: RetosSubtareasInput;
                Update: Partial<RetosSubtareasInput>;
            };
            retos_usuarios: {
                Row: RetosUsuarios;
                Insert: RetosUsuariosInput;
                Update: Partial<RetosUsuariosInput>;
            };
            reunion_empresas_asociadas: {
                Row: ReunionEmpresasAsociadas;
                Insert: ReunionEmpresasAsociadasInput;
                Update: Partial<ReunionEmpresasAsociadasInput>;
            };
            reunion_personas_asociadas: {
                Row: ReunionPersonasAsociadas;
                Insert: ReunionPersonasAsociadasInput;
                Update: Partial<ReunionPersonasAsociadasInput>;
            };
            reunion_speaker_asignaciones: {
                Row: ReunionSpeakerAsignaciones;
                Insert: ReunionSpeakerAsignacionesInput;
                Update: Partial<ReunionSpeakerAsignacionesInput>;
            };
            reuniones: {
                Row: Reuniones;
                Insert: ReunionesInput;
                Update: Partial<ReunionesInput>;
            };
            sops_y_guias: {
                Row: SopsYGuias;
                Insert: SopsYGuiasInput;
                Update: Partial<SopsYGuiasInput>;
            };
            tareas: {
                Row: Tareas;
                Insert: TareasInput;
                Update: Partial<TareasInput>;
            };
            tareas_clientes: {
                Row: TareasClientes;
                Insert: TareasClientesInput;
                Update: Partial<TareasClientesInput>;
            };
            tareas_clientes_responsables: {
                Row: TareasClientesResponsables;
                Insert: TareasClientesResponsablesInput;
                Update: Partial<TareasClientesResponsablesInput>;
            };
            tareas_empresas: {
                Row: TareasEmpresas;
                Insert: TareasEmpresasInput;
                Update: Partial<TareasEmpresasInput>;
            };
            tareas_etiquetas: {
                Row: TareasEtiquetas;
                Insert: TareasEtiquetasInput;
                Update: Partial<TareasEtiquetasInput>;
            };
            thread_message_counts: {
                Row: ThreadMessageCounts;
                Insert: ThreadMessageCountsInput;
                Update: Partial<ThreadMessageCountsInput>;
            };
            threads: {
                Row: Threads;
                Insert: ThreadsInput;
                Update: Partial<ThreadsInput>;
            };
            threads_metadata: {
                Row: ThreadsMetadata;
                Insert: ThreadsMetadataInput;
                Update: Partial<ThreadsMetadataInput>;
            };
            threads_resumen: {
                Row: ThreadsResumen;
                Insert: ThreadsResumenInput;
                Update: Partial<ThreadsResumenInput>;
            };
            ticket_mensajes: {
                Row: TicketMensajes;
                Insert: TicketMensajesInput;
                Update: Partial<TicketMensajesInput>;
            };
            tickets: {
                Row: Tickets;
                Insert: TicketsInput;
                Update: Partial<TicketsInput>;
            };
            tools: {
                Row: Tools;
                Insert: ToolsInput;
                Update: Partial<ToolsInput>;
            };
            usuarios: {
                Row: Usuarios;
                Insert: UsuariosInput;
                Update: Partial<UsuariosInput>;
            };
            workflow_errores: {
                Row: WorkflowErrores;
                Insert: WorkflowErroresInput;
                Update: Partial<WorkflowErroresInput>;
            };
            workflows: {
                Row: Workflows;
                Insert: WorkflowsInput;
                Update: Partial<WorkflowsInput>;
            };
        };
    };
}
