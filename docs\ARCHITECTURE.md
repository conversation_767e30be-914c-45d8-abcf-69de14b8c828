# Application Architecture

This document outlines the technical architecture, technology stack, component responsibilities, and data flows for the Aceleralia Customer Portal.

*(This is a living document. Update it as the architecture evolves, new components are added, or significant technical decisions are made.)*

## 1. Technology Stack

The application is built on a modern, unified, and scalable technology stack, prioritizing developer efficiency and robustness.

*   **Frontend:**
    *   **Framework:** React (with Vite)
    *   **Language:** TypeScript
    *   **State Management:** TanStack Query (for server state), Zustand (for global client state)
    *   **Styling:** Tailwind CSS v4 with Vite plugin integration, custom component library with utility classes
    *   **Key Libraries:** Charting library (e.g., Recharts), Data Grid component.

*   **Backend:**
    *   **Framework:** NestJS (on Node.js)
    *   **Language:** TypeScript
    *   **Database:** Supabase (PostgreSQL)
    *   **Authentication:** Supabase Auth (JWT-based)

*   **Infrastructure & Services:**
    *   **Orchestration:** n8n for asynchronous workflows (AI processing, notifications).
    *   **Storage:** Supabase Storage (for temporary "inbox" uploads) and Google Cloud Storage (for permanent, scalable storage).
    *   **Deployment:** Coolify, self-hosted on Oracle Cloud.
    *   **Reverse Proxy:** Traefik, managed by Coolify.

## 2. Frontend Responsibilities

The frontend is a Single Page Application (SPA) responsible for the entire user interface and experience. Its core duties are:

*   **User Interaction:** Rendering all UI components, including dashboards, forms, data grids, and challenge cards.
*   **State Management:** Managing server state (data fetching, caching, mutations) with TanStack Query and global client state (user session, UI toggles) with Zustand.
*   **API Communication:** Interacting with the backend via a type-safe API client to fetch data and submit user actions.
*   **Authentication:** Handling the login flow, storing JWTs securely, and managing session state.
*   **Real-time Updates:** Subscribing to Supabase Realtime channels to update components like the user ranking live.
*   **Component-Based UI:** Building the interface from a library of shared, reusable components (`<RetoCard />`, `<DataGrid />`, `<FileUpload />`, etc.) to ensure consistency and speed of development.

## 3. Backend Responsibilities

The backend is a NestJS application that acts as the central nervous system, handling all business logic and data orchestration.

*   **API Endpoints:** Providing a secure RESTful API for all frontend operations, with clear, role-protected routes (e.g., `/manager/*`).
*   **Business Logic:** Encapsulating core business rules within modular services (`GamificationService`, `StorageService`, `DashboardService`).
*   **Database Abstraction:** Funneling all database operations through a single `DatabaseService` that interacts with the Supabase client, abstracting the connection logic.
*   **Service Orchestration:** Triggering external workflows on n8n via a dedicated `N8N_WebhookService` for heavy, asynchronous tasks like video analysis.
*   **Authentication & Authorization:** Validating user credentials against Supabase Auth and enforcing role-based access control using NestJS Guards.
*   **External Service Integration:** Managing connections to Supabase and Google Cloud Storage through abstracted services, decoupling the core application from third-party implementations.

## 4. Data Flow Example (Asynchronous Video Upload)

This flow illustrates the decoupled architecture designed for a responsive user experience.

1.  **Frontend:** The user interacts with the `<FileUpload />` component within a challenge, selects a video, and clicks "Submit". The component calls an API client function.
2.  **API Client:** Sends a `POST` request with the file to the `/grabaciones` endpoint on the backend. The UI shows a loading indicator.
3.  **Backend (`GrabacionesController`):** The controller receives the request and immediately passes it to the `GrabacionesService`.
4.  **Backend (`GrabacionesService`):**
    a.  Calls `StorageService.uploadToTemporal(file)` which uploads the video to the Supabase Storage "inbox".
    b.  Calls `DatabaseService.create('grabaciones', ...)` to create a record in the database with `estado_procesamiento` set to `pendiente`.
    c.  Calls `N8N_WebhookService.triggerWorkflow('procesarVideo', { grabacionId: ... })` to initiate the asynchronous processing.
5.  **Immediate Response:** The backend returns a `201 Created` response to the frontend. The UI hides the loader and shows a success message ("¡Grabación enviada!"). The user can continue using the app.
6.  **n8n Workflow (Asynchronous):**
    a.  The webhook triggers the "Procesador de Grabaciones" workflow.
    b.  The workflow fetches the `grabaciones` record, downloads the file from Supabase Storage, and uploads it to the permanent Google Cloud Storage bucket.
    c.  It updates the `url_almacenamiento` in the database with the new GCS URL.
    d.  It sends the file to an AI service for analysis.
    e.  Based on the result, it updates business tables (e.g., `procesos_clientes`), marks the user's challenge as complete, and calls the `GamificationService` to award points.
    f.  It creates a record in the `notificaciones` table.
    g.  It deletes the original file from the temporary Supabase Storage.
7.  **Frontend Notification:** A real-time notification appears in the UI, informing the user that their submission has been processed and points have been awarded.

## 5. Key Considerations & Assumptions

### 5.a Security Considerations

*   **Role-Based Access Control (RBAC):** Security is enforced at multiple levels. The backend uses NestJS Guards to protect entire API route groups (e.g., `/manager`).
*   **Row Level Security (RLS):** The primary data security mechanism is Supabase's RLS. Policies are defined directly in the database, ensuring that API requests authenticated with a user's JWT can only access the data permitted by their role (`cliente_empleado`, `cliente_gerente`).
*   **Admin-led User Provisioning:** There is no public-facing user registration. All user accounts are created by an `admin_aceleralia` role via a separate, internal tool, preventing unauthorized sign-ups.
*   **Secure Credentials:** All third-party API keys, database connection strings, and other secrets are managed via environment variables and are not hardcoded in the application.

### 5.b Database Schema Highlights

The database is the single source of truth. Key tables include:

*   `usuarios`: Stores user profile information, including their role, points, and gamification streaks.
*   `personas`: Linked to `usuarios`, contains more detailed personal information.
*   `retos_usuarios` & `retos_subtareas`: Manages the challenges assigned to users and their individual tasks.
*   `grabaciones`: A log of all media submissions, tracking their processing status and storage location.
*   `procesos_clientes`: Stores the core business process information extracted from user submissions.
*   `hallazgos_clientes`: Records the insights and inefficiencies identified during the consultancy.
*   `configuracion`: A key-value table to store system-wide settings, such as points awarded for specific actions.

## 6. Deployment Strategy
*   **Platform:** Coolify, self-hosted on Oracle Cloud infrastructure (`************`).
*   **Source Control:** Git, with the main branch being `master`.
*   **CI/CD:** Automated deployments triggered by commits to the `master` branch.
*   **Services:**
    *   **Frontend**
    *   **Backend**
    *   **Reverse Proxy:** Coolify manages Traefik for reverse proxying, routing, and automatic SSL certificate generation via Let's Encrypt. Requires correct DNS A records pointing subdomains to the server IP.
    *   **Environment Variables:** All secrets (API keys, database credentials) must be securely configured as environment variables within the Coolify services. See `backend/.env.example`.
    *   **API Documentation:** Swagger/OpenAPI documentation available at `/api` endpoint for development and testing.
    *   **Security:** CORS configured with environment-based origins, JWT authentication, input validation with DTOs.
    *   **Code Quality:** Zero ESLint/TypeScript errors, comprehensive type safety with custom interfaces for complex types.

## 7. Documentation Structure

*   Located in the `docs/` directory.
*   Includes this `ARCHITECTURE.md`, `PROJECT_OVERVIEW.md`, `SETUP_GUIDE.md`, `INSTRUCTIONS_TEMPLATE.md`.
*   `modulos/` subdirectory for detailed feature specifications.
*   `sprints/` subdirectory for session/sprint planning and logs.

## 8. Current Project File Structure
/
|-- backend/
|   |-- src/
|   |   |-- app.module.ts
|   |   |-- main.ts (with Swagger integration)
|   |   |-- common/
|   |   |   |-- database.module.ts
|   |   |   |-- database.service.ts
|   |   |   |-- database.types.ts (auto-generated)
|   |   |-- features/
|   |   |   |-- auth/
|   |   |   |   |-- dto/
|   |   |   |   |   |-- login.dto.ts
|   |   |   |   |   |-- login-response.dto.ts
|   |   |   |   |-- guards/
|   |   |   |   |   |-- jwt-auth.guard.ts
|   |   |   |   |-- strategies/
|   |   |   |   |   |-- jwt.strategy.ts
|   |   |   |   |-- auth.controller.ts
|   |   |   |   |-- auth.module.ts
|   |   |   |   |-- auth.service.ts
|   |   |-- grabaciones/
|   |   |   |-- dto/
|   |   |   |   |-- create-grabacion.dto.ts (with Swagger docs)
|   |   |   |-- grabaciones.controller.ts (with Swagger docs)
|   |   |   |-- grabaciones.module.ts
|   |   |   |-- grabaciones.service.ts
|   |   |-- shared/
|   |   |   |-- gamification.service.ts (with JSDoc)
|   |   |   |-- n8n-webhook.service.ts
|   |   |   |-- shared.module.ts
|   |   |   |-- storage.service.ts
|-- frontend/
|   |-- src/
|   |   |-- App.tsx
|   |   |-- main.tsx
|   |   |-- index.css (Tailwind CSS imports)
|   |   |-- components/
|   |   |   |-- ui/
|   |   |   |   |-- LoadingSpinner.tsx
|   |   |   |   |-- ToastNotification.tsx
|   |   |   |   |-- GlobalToast.tsx
|   |   |   |   |-- button.tsx
|   |   |   |   |-- button-variants.ts
|   |   |   |   |-- input.tsx
|   |   |   |   |-- label.tsx
|   |   |   |   |-- index.ts
|   |   |-- features/
|   |   |   |-- auth/
|   |   |   |   |-- components/
|   |   |   |   |   |-- AuthLayout.tsx
|   |   |   |   |   |-- ForgotPasswordForm.tsx
|   |   |   |   |   |-- LoginForm.tsx
|   |   |   |   |   |-- PersistLogin.tsx
|   |   |   |   |   |-- RequireAuth.tsx
|   |   |   |   |   |-- ResetPasswordForm.tsx
|   |   |   |   |-- hooks/
|   |   |   |   |   |-- useAuth.ts
|   |   |   |   |-- store/
|   |   |   |   |   |-- useAuthStore.ts
|   |   |-- hooks/
|   |   |   |-- useNotifications.ts
|   |   |   |-- useUploadGrabacion.ts
|   |   |-- lib/
|   |   |   |-- api.ts
|   |   |   |-- react-query.ts
|   |   |   |-- supabase.ts
|   |   |-- pages/
|   |   |   |-- ForgotPasswordPage.tsx
|   |   |   |-- LoginPage.tsx
|   |   |   |-- ResetPasswordPage.tsx
|   |   |   |-- demo/
|   |   |-- store/
|   |   |   |-- session.ts
|   |   |   |-- toast.ts (updated with warning type)
|   |   |-- styles/
|   |   |   |-- components.css (custom component styles)
|   |-- tailwind.config.js (Tailwind v4 configuration)
|   |-- postcss.config.js (minimal PostCSS config)
|   |-- vite.config.ts (with @tailwindcss/vite plugin)
|-- packages/
|   |-- types/
|       |-- src/
|           |-- index.ts
|-- docs/
|   |-- ARCHITECTURE.md
|   |-- DATABASE_SCHEMA.md
|   |-- PROJECT_OVERVIEW.md
|   |-- SETUP_GUIDE.md
|   |-- modulos/
|   |-- sprints/
