# Feature: 1. Authentication and User Onboarding

## 1. Overview

This module governs how users gain access to the portal and how their identities are managed within the system. It covers the entire lifecycle from administrative user creation to session management. The primary goal is to provide a secure, seamless, and controlled access mechanism, ensuring that users can only see the data and perform the actions appropriate for their role (`cliente_empleado` or `cliente_gerente`).

## 2. Requirements

*   **Requirement 1 (Admin-led Provisioning):** User accounts cannot be self-registered. They must be created by an `admin_aceleralia` role from a separate administrative interface. The creation process involves creating a `usuarios` record and linking it to a `personas` record.
*   **Requirement 2 (Secure Login):** Users must be able to log in using their email and a password. The system will use Supabase Auth for all authentication processes.
*   **Requirement 3 (Role-Based Access):** Once authenticated, the user's role (`cliente_gerente` or `cliente_empleado`) must be loaded into the session to control access to different application modules (e.g., Manager's Dashboard).
*   **Requirement 4 (Session Management):** The system must use JSON Web Tokens (JWT) for managing user sessions, ensuring they are secure and can be refreshed or invalidated upon logout.
*   **Requirement 5 (Password Recovery):** Users must have a way to securely reset their password if they forget it. This flow is handled by Supabase Auth's built-in functionality.

## 3. UI/UX Design

*   **Key Screens/Components:**
    *   **Login Page (`/login`):** A minimalist page featuring the company logo, fields for email and password, a "Sign In" button, and a "Forgot Password?" link.
    *   **Forgot Password Page (`/forgot-password`):** A simple form with an email field to initiate the password reset process.
    *   **Reset Password Page (`/update-password`):** A form with fields for the new password and password confirmation.
*   **User Flow (Login):**
    1.  User navigates to the login page.
    2.  Enters email and password.
    3.  Clicks "Sign In".
    4.  On success, the user is redirected to their main dashboard (`/inicio`).
    5.  On failure, an error message is displayed.
*   **User Flow (Password Recovery):**
    1.  User clicks the "¿Olvidaste tu contraseña?" link on the login page.
    2.  User is redirected to the `/forgot-password` page and enters their email.
    3.  User receives an email with a link to reset their password.
    4.  User clicks the link and is redirected to the `/update-password` page.
    5.  User enters and confirms their new password.
    6.  On success, the user is redirected to the login page with a success message.

## 4. Technical Details

*   **Frontend:**
    *   **Relevant Components:** `LoginForm`, `AuthLayout`, `ForgotPasswordForm`, `ResetPasswordForm`.
    *   **State Management:** A global `AuthContext` or Zustand store will hold the user object, session token, and authentication status (`isAuthenticated`). TanStack Query will manage the login mutation.
    *   **API Interactions:** A single `POST` request to the backend's `/auth/login` endpoint.
*   **Backend:**
    *   **Relevant API Routes:**
        *   `POST /auth/login`: Receives email and password, validates them against Supabase Auth, and on success, returns a JWT and user data.
        *   `POST /auth/logout`: Invalidates the user's session.
    *   **Services Used:** `AuthService` (to interact with Supabase Auth), `DatabaseService` (to fetch user details from the `usuarios` table).
    *   **Database Tables Involved:** `usuarios`, `personas`.
    *   **External Services:** Supabase Auth.
*   **Data Models:**
    *   **TypeScript (Frontend):**
        ```typescript
        interface User {
          id: string;
          email: string;
          nombre: string;
          apellidos: string;
          rol: 'cliente_gerente' | 'cliente_empleado';
          puntos: number;
          racha_actual: number;
        }
        ```

## 5. Acceptance Criteria

*   Given a valid user exists, when they enter the correct credentials, then they should be successfully logged in and redirected to the dashboard.
*   Given a user enters incorrect credentials, when they attempt to log in, then an appropriate error message should be displayed.
*   Given a logged-in user, when they refresh the page, then they should remain logged in.
*   Given a logged-in user, when they click the "Logout" button, then their session should be terminated and they should be redirected to the login page.

## 6. Notes & Decisions

*   The user creation and role assignment logic is handled outside of this project's scope (in an internal admin tool), simplifying the portal's responsibility to only authentication and session management.
*   We will rely entirely on Supabase's built-in Row Level Security (RLS) policies, enforced by the user's role, to secure data access at the database level. The backend will pass the user's JWT to Supabase with every request.